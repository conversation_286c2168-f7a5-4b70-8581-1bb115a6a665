import { renderHook } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useDataPrefetch } from '@/hooks/useDataPrefetch'
import { QUERY_KEYS } from '@/constants/queryKeys'

vi.useFakeTimers()

const prefetchQuery = vi.fn()

vi.mock('@tanstack/react-query', () => ({
  useQueryClient: () => ({ prefetchQuery }),
}))

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id: 'user-1' } }),
}))

describe('useDataPrefetch', () => {
  beforeEach(() => {
    prefetchQuery.mockClear()
  })

  it('prefetches all finance datasets', () => {
    renderHook(() => useDataPrefetch())
    vi.runAllTimers()

    const expectedKeys = [
      [QUERY_KEYS.INCOMES, 'user-1'],
      [QUERY_KEYS.EXPENSES, 'user-1'],
      [QUERY_KEYS.DEBTS, 'user-1'],
      [QUERY_KEYS.SUBSCRIPTIONS, 'user-1'],
      [QUERY_KEYS.REIMBURSEMENTS, 'user-1'],
      [QUERY_KEYS.FINANCIAL_GOALS_ALIAS, 'user-1'],
      [QUERY_KEYS.PAYMENT_RECORDS, 'user-1'],
    ]

    expectedKeys.forEach(key => {
      expect(prefetchQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          queryKey: key,
          staleTime: 1000 * 60 * 10,
        })
      )
    })

    expect(prefetchQuery).toHaveBeenCalledTimes(expectedKeys.length)
  })
})
