/* ==============================================
   SOLUCIONES ESPECÍFICAS PARA SCROLL MÓVIL
   ============================================== */

/* Variables para scroll móvil */
:root {
  --mobile-header-height: 120px;
  --mobile-tabs-height: 60px;
  --mobile-content-height: calc(100vh - var(--mobile-header-height) - var(--mobile-tabs-height));
}

/* ==============================================
   CONTENEDOR PRINCIPAL DEL DASHBOARD
   ============================================== */

@media (max-width: 767px) {
  /* Contenedor principal con scroll habilitado */
  .dashboard-main-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* Header del dashboard - altura fija */
  .dashboard-header {
    flex-shrink: 0;
    height: var(--mobile-header-height);
    overflow: hidden;
  }

  /* Contenedor de tabs - altura fija */
  .dashboard-tabs-container {
    flex-shrink: 0;
    height: var(--mobile-tabs-height);
    overflow: hidden;
  }

  /* Contenido de tabs - scrollable */
  .dashboard-content-container {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  /* Contenido específico de cada tab */
  .dashboard-tab-content {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    padding: 0.75rem;
    padding-bottom: 2rem;
  }

  /* Ocultar scrollbar pero mantener funcionalidad */
  .dashboard-tab-content::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  .dashboard-tab-content {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  /* ==============================================
     ANÁLISIS TAB - SCROLL ESPECÍFICO
     ============================================== */

  /* Contenedor del análisis con scroll optimizado */
  .analysis-tab-container {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    padding: 0;
  }

  /* Espaciado interno del análisis */
  .analysis-content {
    padding: 0.75rem;
    padding-bottom: 2rem;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  /* Cards de análisis sin restricciones de altura */
  .analysis-content .card {
    height: auto !important;
    min-height: auto !important;
    max-height: none !important;
    overflow: visible !important;
  }

  /* Contenido de cards flexible */
  .analysis-content .card-content {
    height: auto !important;
    overflow: visible !important;
    padding: 1rem;
  }

  /* Gráficos responsivos */
  .analysis-content .recharts-wrapper {
    overflow: visible !important;
    height: auto !important;
  }

  .analysis-content .recharts-surface {
    overflow: visible !important;
  }

  /* ==============================================
     TABS INTERNOS (DENTRO DE ANÁLISIS)
     ============================================== */

  /* Tabs internos sin altura fija */
  .analysis-content [role="tablist"] {
    flex-shrink: 0;
  }

  .analysis-content [role="tabpanel"] {
    height: auto !important;
    overflow: visible !important;
    flex: 1;
  }

  /* Contenido de tabs internos */
  .analysis-content .tabs-content {
    height: auto !important;
    overflow: visible !important;
  }

  /* ==============================================
     COMPONENTES ESPECÍFICOS
     ============================================== */

  /* InteractiveCharts sin restricciones */
  .interactive-charts-container {
    height: auto !important;
    overflow: visible !important;
  }

  .interactive-charts-container .card {
    height: auto !important;
    min-height: 400px;
  }

  /* AdvancedFinancialAnalysis flexible */
  .advanced-financial-analysis {
    height: auto !important;
    overflow: visible !important;
  }

  /* DiagnosticCharts responsive */
  .diagnostic-charts-container {
    height: auto !important;
    overflow: visible !important;
  }

  /* PaymentTrendsSection sin restricciones */
  .payment-trends-container {
    height: auto !important;
    overflow: visible !important;
  }

  /* HealthIndicators flexible */
  .health-indicators-container {
    height: auto !important;
    overflow: visible !important;
  }

  /* ==============================================
     PREVENCIÓN DE PROBLEMAS COMUNES
     ============================================== */

  /* Evitar que elementos tengan altura 100vh */
  .analysis-content * {
    max-height: none !important;
  }

  /* Evitar overflow hidden en contenedores principales */
  .analysis-content .container,
  .analysis-content .wrapper,
  .analysis-content .content {
    overflow: visible !important;
  }

  /* Asegurar que los flex containers no restrinjan altura */
  .analysis-content .flex,
  .analysis-content .grid {
    height: auto !important;
    min-height: auto !important;
  }

  /* ==============================================
     SCROLL SUAVE Y OPTIMIZADO
     ============================================== */

  /* Scroll momentum en iOS */
  .dashboard-tab-content,
  .analysis-tab-container {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Prevenir bounce scroll */
  .dashboard-tab-content {
    overscroll-behavior-y: contain;
  }

  /* Scroll suave en Android */
  .dashboard-tab-content {
    scroll-behavior: smooth;
  }

  /* ==============================================
     INDICADORES DE SCROLL
     ============================================== */

  /* Sombra superior cuando hay scroll */
  .dashboard-tab-content::before {
    content: '';
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), transparent);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .dashboard-tab-content.scrolled::before {
    opacity: 1;
  }

  /* ==============================================
     COMPATIBILIDAD CON DIFERENTES NAVEGADORES
     ============================================== */

  /* Safari iOS */
  @supports (-webkit-touch-callout: none) {
    .dashboard-tab-content {
      -webkit-overflow-scrolling: touch;
    }
  }

  /* Chrome Android */
  @supports (overscroll-behavior: contain) {
    .dashboard-tab-content {
      overscroll-behavior: contain;
    }
  }

  /* Firefox Mobile */
  @-moz-document url-prefix() {
    .dashboard-tab-content {
      scrollbar-width: none;
    }
  }
}

/* ==============================================
   LANDSCAPE MODE OPTIMIZATIONS
   ============================================== */

@media (max-width: 767px) and (orientation: landscape) {
  :root {
    --mobile-header-height: 80px;
    --mobile-tabs-height: 50px;
  }

  .dashboard-tab-content {
    padding: 0.5rem;
    padding-bottom: 1rem;
  }

  .analysis-content {
    gap: 1rem;
    padding: 0.5rem;
  }
}

/* ==============================================
   DEBUG MODE (OPCIONAL)
   ============================================== */

.debug-scroll {
  border: 2px solid red !important;
  background: rgba(255, 0, 0, 0.1) !important;
}

.debug-scroll::after {
  content: 'SCROLL DEBUG';
  position: absolute;
  top: 0;
  right: 0;
  background: red;
  color: white;
  padding: 2px 4px;
  font-size: 10px;
  z-index: 9999;
}
