
import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { AdvancedFinancialAnalysis } from '../AdvancedFinancialAnalysis';
import { InteractiveCharts } from '../InteractiveCharts';
import { DiagnosticCharts } from '../DiagnosticCharts';
import { PaymentTrendsSection } from '../PaymentTrendsSection';
import { useDashboardData } from '../../hooks/useDashboardData';

const HealthIndicators = React.lazy(() => 
  import('../HealthIndicators').then(module => ({ default: module.HealthIndicators }))
);

export const AnalysisTab: React.FC = () => {
  const {
    netIncome,
    totalMonthlyPayments,
    totalDebt,
    savingsRate,
    debtToIncomeRatio,
    emergencyFundMonths,
    paymentBreakdown
  } = useDashboardData();

  return (
    <div className="space-y-6">
      {/* Análisis financiero consolidado */}
      <AdvancedFinancialAnalysis />

      {/* Sección de tendencias de pagos con funcionalidad completa */}
      <PaymentTrendsSection />

      {/* Gráficos interactivos optimizados */}
      <InteractiveCharts className="col-span-full" />

      {/* Gráficos diagnósticos mejorados con datos de pagos programados */}
      <DiagnosticCharts
        netIncome={netIncome}
        totalMonthlyPayments={totalMonthlyPayments}
        totalDebt={totalDebt}
        savingsRate={savingsRate}
        debtToIncomeRatio={debtToIncomeRatio}
        emergencyFundMonths={emergencyFundMonths}
        paymentBreakdown={paymentBreakdown}
      />

      {/* Indicadores de salud con lazy loading */}
      <Suspense fallback={<Skeleton className="h-64 w-full" />}>
        <HealthIndicators 
          savingsRate={savingsRate}
          debtToIncomeRatio={debtToIncomeRatio}
          emergencyFundMonths={emergencyFundMonths}
        />
      </Suspense>
    </div>
  );
};
