import React from 'react';
import { Button } from '@/components/ui/button';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { cn } from '@/lib/utils';

interface MobileOptimizedButtonProps {
  children: React.ReactNode;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon' | 'xs';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  mobileSize?: 'xs' | 'sm' | 'default';
  mobileText?: string;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export const MobileOptimizedButton: React.FC<MobileOptimizedButtonProps> = ({
  children,
  variant = 'default',
  size = 'default',
  className,
  onClick,
  disabled,
  type = 'button',
  mobileSize = 'xs',
  mobileText,
  icon,
  fullWidth = false,
  ...props
}) => {
  const { isMobile } = useBreakpoint();

  const getMobileSize = () => {
    if (!isMobile) return size;
    return mobileSize;
  };

  const getButtonText = () => {
    if (isMobile && mobileText) {
      return mobileText;
    }
    return children;
  };

  const getMobileClasses = () => {
    if (!isMobile) return '';
    
    const baseClasses = 'transition-all duration-200 touch-manipulation active:scale-95';
    
    switch (mobileSize) {
      case 'xs':
        return cn(
          baseClasses,
          'text-xs px-2 py-1.5 h-8 font-semibold',
          fullWidth && 'min-w-0 flex-1'
        );
      case 'sm':
        return cn(
          baseClasses,
          'text-xs px-3 py-2 h-9 font-semibold',
          fullWidth && 'min-w-0 flex-1'
        );
      default:
        return cn(
          baseClasses,
          'text-sm px-4 py-2 h-10 font-semibold',
          fullWidth && 'min-w-0 flex-1'
        );
    }
  };

  return (
    <Button
      variant={variant}
      size={getMobileSize()}
      className={cn(getMobileClasses(), className)}
      onClick={onClick}
      disabled={disabled}
      type={type}
      {...props}
    >
      {icon && (
        <span className={cn(
          'flex-shrink-0',
          isMobile ? 'w-3 h-3' : 'w-4 h-4',
          children && (isMobile ? 'mr-1' : 'mr-2')
        )}>
          {icon}
        </span>
      )}
      <span className={cn(
        isMobile && 'truncate',
        fullWidth && 'text-center'
      )}>
        {getButtonText()}
      </span>
    </Button>
  );
};

// Componente para grupos de botones horizontales
interface MobileButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  columns?: 2 | 3 | 4;
  spacing?: 'tight' | 'normal' | 'loose';
}

export const MobileButtonGroup: React.FC<MobileButtonGroupProps> = ({
  children,
  className,
  columns = 2,
  spacing = 'normal'
}) => {
  const { isMobile } = useBreakpoint();

  const getSpacing = () => {
    switch (spacing) {
      case 'tight':
        return 'gap-0.5';
      case 'loose':
        return 'gap-2';
      default:
        return 'gap-1';
    }
  };

  const getMobileClasses = () => {
    if (!isMobile) {
      return cn('flex flex-wrap', getSpacing());
    }

    return cn(
      'grid w-full',
      `grid-cols-${columns}`,
      getSpacing()
    );
  };

  return (
    <div className={cn(getMobileClasses(), className)}>
      {children}
    </div>
  );
};

// Componente para tabs móviles optimizados
interface MobileTabsListProps {
  children: React.ReactNode;
  className?: string;
  columns?: number;
  variant?: 'default' | 'pills' | 'underline';
}

export const MobileTabsList: React.FC<MobileTabsListProps> = ({
  children,
  className,
  columns,
  variant = 'default'
}) => {
  const { isMobile } = useBreakpoint();

  const getVariantClasses = () => {
    switch (variant) {
      case 'pills':
        return isMobile 
          ? 'bg-gray-100 dark:bg-gray-800 p-0.5 gap-0.5 rounded-lg'
          : '';
      case 'underline':
        return 'border-b border-gray-200 dark:border-gray-700';
      default:
        return isMobile 
          ? 'bg-gray-100 dark:bg-gray-800 p-0.5 gap-0.5 rounded-md'
          : '';
    }
  };

  const getMobileClasses = () => {
    if (!isMobile) {
      return cn('grid w-full', className);
    }

    const colsClass = columns ? `grid-cols-${columns}` : 'grid-cols-auto';
    
    return cn(
      'grid w-full h-auto',
      colsClass,
      getVariantClasses(),
      className
    );
  };

  return (
    <div className={getMobileClasses()}>
      {children}
    </div>
  );
};

// Componente para triggers de tabs móviles
interface MobileTabsTriggerProps {
  children: React.ReactNode;
  value: string;
  className?: string;
  icon?: React.ReactNode;
  badge?: string | number;
  isActive?: boolean;
}

export const MobileTabsTrigger: React.FC<MobileTabsTriggerProps> = ({
  children,
  value,
  className,
  icon,
  badge,
  isActive = false
}) => {
  const { isMobile } = useBreakpoint();

  const getMobileClasses = () => {
    if (!isMobile) return className;

    return cn(
      'text-xs py-2 px-1 min-h-[36px] font-medium rounded-md',
      'transition-all duration-200 touch-manipulation active:scale-95',
      'data-[state=active]:bg-white data-[state=active]:shadow-sm',
      'dark:data-[state=active]:bg-gray-700',
      className
    );
  };

  return (
    <button
      data-state={isActive ? 'active' : 'inactive'}
      className={getMobileClasses()}
      data-value={value}
    >
      {icon && (
        <span className={cn(
          'flex-shrink-0',
          isMobile ? 'w-3 h-3 mb-0.5' : 'w-4 h-4 mr-2'
        )}>
          {icon}
        </span>
      )}
      <span className={cn(
        isMobile && 'text-[10px] leading-tight font-semibold truncate'
      )}>
        {children}
      </span>
      {badge && (
        <span className={cn(
          'ml-1 px-1.5 py-0.5 text-xs bg-gray-200 dark:bg-gray-600 rounded-full',
          isMobile && 'text-[8px] px-1 py-0.5'
        )}>
          {badge}
        </span>
      )}
    </button>
  );
};
