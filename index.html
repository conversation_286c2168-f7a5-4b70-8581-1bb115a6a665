<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FinanzApp - Tu Gestor Financiero Personal</title>
    <meta name="description" content="Controla tus finanzas como un profesional. Gestiona ingresos, gastos, deudas y metas financieras en una sola aplicación." />
    <meta name="author" content="FinanzApp" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="FinanzApp - Tu Gestor Financiero Personal" />
    <meta property="og:description" content="La aplicación más completa para gestionar tus finanzas personales. Controla ingresos, gastos, deudas y alcanza tus metas financieras." />
    <meta property="og:image" content="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=630&fit=crop&crop=center" />
    <meta property="og:image:width" content="1200" />
        <meta property="og:image:height" content="630" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="FinanzApp - Tu Gestor Financiero Personal" />
    <meta name="twitter:description" content="La aplicación más completa para gestionar tus finanzas personales. Controla ingresos, gastos, deudas y alcanza tus metas financieras." />
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=630&fit=crop&crop=center" />

    <!-- Favicon limpio sin versioning -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="manifest" href="/site.webmanifest">
    
    <!-- Microsoft/Windows soporte -->
    <meta name="msapplication-TileColor" content="#8b5cf6">
    <meta name="msapplication-config" content="/browserconfig.xml">
    
    <!-- Additional meta tags for better SEO -->
    <meta name="keywords" content="finanzas, gestión financiera, presupuesto, ahorro, inversión, deudas, metas financieras" />
    <meta name="theme-color" content="#8b5cf6" />
  </head>

    <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
