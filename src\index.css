/* Base styles and CSS variables */
@import './styles/base.css';

/* Dark mode core styles */
@import './styles/dark-mode.css';

/* Component-specific dark mode overrides */
@import './styles/components-dark.css';

/* Form and input dark mode styles */
@import './styles/forms-dark.css';

/* Sidebar dark mode styles */
@import './styles/sidebar-dark.css';

/* Page transition styles */
@import './styles/page-transitions.css';

/* Mobile optimizations - NUEVA IMPORTACIÓN */
@import './styles/mobile-optimizations.css';

/* Component responsive styles - NUEVA IMPORTACIÓN */
@import './styles/components-responsive.css';

/* Mobile button optimizations - NUEVA IMPORTACIÓN */
@import './styles/mobile-buttons.css';

/* Tailwind base styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global mobile optimizations */
@layer base {
  /* Viewport setup */
  html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Mobile-first responsive typography */
  @media (max-width: 767px) {
    html {
      font-size: 14px;
    }
    
    h1 { font-size: 1.5rem; }
    h2 { font-size: 1.25rem; }
    h3 { font-size: 1.125rem; }
    h4 { font-size: 1rem; }
    h5 { font-size: 0.875rem; }
    h6 { font-size: 0.75rem; }
  }
}

@layer components {
  /* Responsive utility classes */
  .mobile-only {
    @apply block md:hidden;
  }
  
  .tablet-only {
    @apply hidden md:block lg:hidden;
  }
  
  .desktop-only {
    @apply hidden lg:block;
  }
  
  .mobile-tablet-only {
    @apply block lg:hidden;
  }
  
  .tablet-desktop-only {
    @apply hidden md:block;
  }

  /* Mobile-optimized containers */
  .mobile-container {
    @apply px-2 py-1 md:px-4 md:py-2 lg:px-6 lg:py-4;
  }
  
  .mobile-section {
    @apply space-y-2 md:space-y-4 lg:space-y-6;
  }
  
  .mobile-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 md:gap-4 lg:gap-6;
  }

  /* Touch-optimized interactions */
  .touch-button {
    @apply min-h-[44px] min-w-[44px] touch-manipulation select-none;
    @apply active:scale-95 transition-transform duration-150;
  }
  
.touch-card {
    @apply touch-manipulation select-none;
    @apply hover:shadow-lg active:scale-[0.98] transition-all duration-200;
  }
}

/* Extra small screen adjustments */
@media (max-width: 640px) {
  @layer base {
    html {
      font-size: 13px;
    }
  }

  @layer components {
    .mobile-container {
      @apply px-1 py-0.5;
    }

    .mobile-section {
      @apply space-y-1;
    }

    .mobile-grid {
      @apply gap-1;
    }
  }
}
