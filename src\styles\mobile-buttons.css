/* ==============================================
   OPTIMIZACIONES ESPECÍFICAS PARA BOTONES MÓVILES
   ============================================== */

/* Variables específicas para botones móviles */
:root {
  --mobile-button-height: 44px;
  --mobile-button-small: 36px;
  --mobile-button-xs: 32px;
  --mobile-button-radius: 8px;
  --mobile-button-font-xs: 10px;
  --mobile-button-font-sm: 12px;
  --mobile-button-font-base: 14px;
  --mobile-tap-highlight: rgba(59, 130, 246, 0.1);
}

/* ==============================================
   BOTONES BASE MÓVILES
   ============================================== */

@media (max-width: 767px) {
  /* Todos los botones tienen feedback táctil */
  button,
  [role="button"],
  .btn,
  .button {
    -webkit-tap-highlight-color: var(--mobile-tap-highlight);
    touch-action: manipulation;
    user-select: none;
    -webkit-user-select: none;
    transition: all 0.15s ease;
  }

  /* Feedback táctil activo */
  button:active,
  [role="button"]:active,
  .btn:active,
  .button:active {
    transform: scale(0.95);
    opacity: 0.8;
  }

  /* Botones pequeños para espacios reducidos */
  .btn-mobile-xs {
    height: var(--mobile-button-xs);
    font-size: var(--mobile-button-font-xs);
    padding: 0.25rem 0.5rem;
    border-radius: calc(var(--mobile-button-radius) - 2px);
    font-weight: 600;
    line-height: 1.2;
  }

  .btn-mobile-sm {
    height: var(--mobile-button-small);
    font-size: var(--mobile-button-font-sm);
    padding: 0.375rem 0.75rem;
    border-radius: var(--mobile-button-radius);
    font-weight: 600;
  }

  .btn-mobile-base {
    height: var(--mobile-button-height);
    font-size: var(--mobile-button-font-base);
    padding: 0.5rem 1rem;
    border-radius: var(--mobile-button-radius);
    font-weight: 600;
  }
}

/* ==============================================
   GRUPOS DE BOTONES ESPECÍFICOS
   ============================================== */

@media (max-width: 767px) {
  /* Botones de análisis - Grid 2x2 */
  .analysis-button-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.25rem;
    width: 100%;
  }

  .analysis-button-grid button {
    height: var(--mobile-button-xs);
    font-size: var(--mobile-button-font-xs);
    padding: 0.25rem 0.375rem;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: calc(var(--mobile-button-radius) - 2px);
    font-weight: 600;
  }

  /* Tabs principales del dashboard */
  .dashboard-tabs-mobile {
    background-color: rgb(243 244 246);
    border-radius: var(--mobile-button-radius);
    padding: 0.125rem;
    gap: 0.125rem;
  }

  .dashboard-tab-mobile {
    min-height: var(--mobile-button-height);
    font-size: var(--mobile-button-font-xs);
    font-weight: 600;
    border-radius: calc(var(--mobile-button-radius) - 2px);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
    padding: 0.375rem 0.25rem;
    transition: all 0.2s ease;
  }

  .dashboard-tab-mobile:active {
    transform: scale(0.95);
  }

  .dashboard-tab-mobile[data-state="active"] {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .dashboard-tab-icon-mobile {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
  }

  .dashboard-tab-label-mobile {
    font-size: var(--mobile-button-font-xs);
    line-height: 1.2;
    text-align: center;
    font-weight: 600;
  }

  /* Tabs secundarios (3 columnas) */
  .secondary-tabs-mobile {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.125rem;
    background-color: rgb(243 244 246);
    border-radius: var(--mobile-button-radius);
    padding: 0.125rem;
  }

  .secondary-tab-mobile {
    height: var(--mobile-button-small);
    font-size: var(--mobile-button-font-sm);
    font-weight: 600;
    border-radius: calc(var(--mobile-button-radius) - 2px);
    padding: 0.375rem 0.25rem;
    transition: all 0.2s ease;
    text-align: center;
  }

  .secondary-tab-mobile:active {
    transform: scale(0.95);
  }

  .secondary-tab-mobile[data-state="active"] {
    background-color: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  /* Botones de acción flotantes */
  .floating-action-mobile {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 1rem);
    right: 1rem;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 40;
  }

  .floating-action-mobile:active {
    transform: scale(0.9);
  }
}

/* ==============================================
   MODO OSCURO PARA BOTONES MÓVILES
   ============================================== */

@media (max-width: 767px) {
  .dark .dashboard-tabs-mobile {
    background-color: rgb(31 41 55);
  }

  .dark .dashboard-tab-mobile[data-state="active"] {
    background-color: rgb(55 65 81);
    color: white;
  }

  .dark .secondary-tabs-mobile {
    background-color: rgb(31 41 55);
  }

  .dark .secondary-tab-mobile[data-state="active"] {
    background-color: rgb(55 65 81);
    color: white;
  }

  .dark .analysis-button-grid button {
    border-color: rgb(75 85 99);
    background-color: rgb(31 41 55);
    color: rgb(229 231 235);
  }

  .dark .analysis-button-grid button:hover {
    background-color: rgb(55 65 81);
  }

  .dark .analysis-button-grid button[data-state="active"] {
    background-color: rgb(59 130 246);
    color: white;
    border-color: rgb(59 130 246);
  }
}

/* ==============================================
   UTILIDADES PARA BOTONES RESPONSIVOS
   ============================================== */

/* Clases utilitarias para aplicar fácilmente */
.mobile-touch-target {
  min-height: var(--mobile-button-height);
  min-width: var(--mobile-button-height);
  touch-action: manipulation;
}

.mobile-button-compact {
  height: var(--mobile-button-xs);
  font-size: var(--mobile-button-font-xs);
  padding: 0.25rem 0.5rem;
  font-weight: 600;
}

.mobile-button-grid-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.25rem;
}

.mobile-button-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.25rem;
}

.mobile-button-grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.25rem;
}

/* Solo aplicar en móvil */
@media (min-width: 768px) {
  .mobile-only-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .mobile-only-grid button {
    width: auto;
    height: auto;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

/* ==============================================
   ANIMACIONES ESPECÍFICAS PARA MÓVIL
   ============================================== */

@media (max-width: 767px) {
  /* Animación de carga para botones */
  .button-loading-mobile {
    position: relative;
    color: transparent;
  }

  .button-loading-mobile::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: button-spin 0.8s linear infinite;
  }

  @keyframes button-spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Animación de éxito */
  .button-success-mobile {
    background-color: rgb(34 197 94) !important;
    transform: scale(1.05);
    transition: all 0.3s ease;
  }

  .button-success-mobile::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
  }
}
