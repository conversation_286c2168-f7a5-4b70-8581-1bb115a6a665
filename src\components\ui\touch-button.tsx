
import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useBreakpoint } from '@/hooks/useBreakpoint';

interface TouchButtonProps extends React.ComponentProps<typeof Button> {
  touchOptimized?: boolean;
}

export const TouchButton = React.forwardRef<
  React.ElementRef<typeof Button>,
  TouchButtonProps
>(({ className, touchOptimized = true, size, ...props }, ref) => {
  const { isMobile, isTablet, isTouchDevice } = useBreakpoint();
  
  // Determinar el tamaño óptimo para touch
  const getOptimalSize = () => {
    if (!touchOptimized) return size;

    if (isMobile) {
      // En móvil, usar el tamaño especificado o default para mejor control
      return size || 'default';
    }

    if (isTablet && isTouchDevice) {
      return size || 'default';
    }

    return size;
  };

  return (
    <Button
      ref={ref}
      size={getOptimalSize()}
      className={cn(
        // Estilos base para touch
        touchOptimized && (isMobile || (isTablet && isTouchDevice)) && [
          'min-h-[44px] min-w-[44px]', // Mínimo Apple HIG
          'active:scale-95 transition-all duration-150', // Feedback táctil
          'select-none touch-manipulation', // Optimización touch
          'focus:ring-2 focus:ring-offset-1', // Mejor accesibilidad
        ],
        // Padding extra en móvil
        isMobile && touchOptimized && 'px-4 py-3 text-sm',
        // Hover states solo en desktop
        !isTouchDevice && 'hover:scale-105',
        className
      )}
      {...props}
    />
  );
});

TouchButton.displayName = 'TouchButton';
