import React, { useState, useCallback, useMemo } from 'react';
import { DollarSign, Clock, CheckCircle, AlertTriangle, TrendingUp } from 'lucide-react';
import { useFinanceData } from '@/hooks/useFinanceData';
import { useBreakpoint } from '@/hooks/useBreakpoint';
import { formatCurrency } from '@/components/ui/numeric-input';
import { useTemporalNavigation } from '../hooks/useTemporalNavigation';
import { useTemporalPayments } from '../hooks/useTemporalPayments';
import { usePaymentsLogic } from '../hooks/usePaymentsLogic';
import { MarkPaymentAsPaidModal } from './MarkPaymentAsPaidModal';
import { PaymentItem as PaymentItemType } from '../types/paymentTypes';
import { usePaymentNotifications } from '../hooks/usePaymentNotifications';
import { usePaymentFilters } from './PaymentFilters';
import { PaymentCalendarView } from './PaymentCalendarView';
import { PaymentTrendsAnalysis } from './PaymentTrendsAnalysis';
import { usePaymentProjections } from '../hooks/usePaymentProjections';
import { usePaymentsPerformance } from '../hooks/usePaymentsPerformance';
import { usePaymentsSynchronization } from '../hooks/usePaymentsSynchronization';
import { useQueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '@/constants/queryKeys';
import { useAuth } from '@/contexts/AuthContext';

// New Subcomponent Imports
import { PaymentsHeader } from './PaymentsHeader';
import { PaymentViewControls } from './PaymentViewControls';
import { PaymentStatsCards } from './PaymentStatsCards';
import { PaymentTabsView } from './PaymentTabsView';


interface PaymentData {
  notes?: string;
  amount?: number;
  interestAmount?: number;
  principalAmount?: number;
  paymentType?: 'minimum' | 'interest_only' | 'custom';
}

export function EnhancedPaymentsSection() {
  const { renderCount } = usePaymentsPerformance('EnhancedPaymentsSection');
  usePaymentsSynchronization();

  const { paymentRecords, isLoading } = useFinanceData();
  const { isMobile } = useBreakpoint();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  const {
    currentPeriod,
    selectedDateInput, // Renamed from selectedDateRange for consistency if used directly
    currentDisplayMode,
    setDateRange, // Renamed from onDateRangeSelect for consistency
    navigateToPreviousMonth,
    navigateToNextMonth,
    navigateToCurrentMonth,
    navigateToMonth,
    getAvailableMonths
  } = useTemporalNavigation();

  const { payments, totals, periodMetrics } = useTemporalPayments(currentPeriod);
  const { handleMarkAsPaid, handleUnmarkAsPaid } = usePaymentsLogic();
  
  // Projections should be sensitive to the currentPeriod's mode and future status
  const {
    projectionAnalysis,
    projectedPaymentsForPeriod,
    hasProjections
  } = usePaymentProjections(currentPeriod);

  // This specific prop for PaymentsHeader will determine if the projection section is shown
  const isProjectionDisplayPeriod = currentPeriod.displayMode === 'month' && currentPeriod.isFutureMonth;

  const [activeTab, setActiveTab] = useState('pending');
  const [selectedPayment, setSelectedPayment] = useState<PaymentItemType | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'calendar' | 'trends'>('list');
  const [selectedCalendarDate, setSelectedCalendarDate] = useState<Date>();

  usePaymentNotifications(payments, {
    enableOverdueAlerts: true,
    enableUpcomingAlerts: true,
    daysBeforeDue: 3
  });

  const allPaymentsList = useMemo(() => [...payments.pending, ...payments.paid, ...payments.overdue], [payments]);
  const { filteredPayments, /* filters, */ setFilters, hasActiveFilters } = usePaymentFilters(allPaymentsList);

  // Function to refresh all payment-related data
  const handleRefreshData = useCallback(async () => {
    if (!user?.id) return;
    
    const queriesToRefresh = [
      [QUERY_KEYS.EXPENSES, user.id],
      [QUERY_KEYS.PAYMENT_RECORDS, user.id],
      [QUERY_KEYS.DEBTS, user.id],
      [QUERY_KEYS.LOANS, user.id],
      [QUERY_KEYS.SUBSCRIPTIONS, user.id],
      [QUERY_KEYS.CREDIT_CARDS, user.id]
    ];

    await Promise.all(
      queriesToRefresh.map(queryKey =>
        queryClient.invalidateQueries({ queryKey, exact: true })
      )
    );
  }, [queryClient, user?.id]);

  const getPaymentsForTab = useCallback((tab: string) => {
    const basePayments = hasActiveFilters ? filteredPayments : allPaymentsList;
    switch (tab) {
      case 'pending': return basePayments.filter(p => p.status === 'pending');
      case 'overdue': return basePayments.filter(p => p.status === 'overdue');
      case 'paid': return basePayments.filter(p => p.status === 'paid');
      default: return basePayments;
    }
  }, [allPaymentsList, filteredPayments, hasActiveFilters]);

  const handlePaymentClick = useCallback((payment: PaymentItemType) => {
    // Allow marking as paid only if not in a future month (for month view)
    // or if the display mode is 'range' (assuming ranges are typically for past/current analysis)
    const canMarkAsPaid = currentPeriod.displayMode === 'range' ||
                         (currentPeriod.displayMode === 'month' && !currentPeriod.isFutureMonth);

    if (payment.status !== 'paid' && canMarkAsPaid) {
      setSelectedPayment(payment);
      setIsModalOpen(true);
    }
  }, [currentPeriod]);

  const handleConfirmPayment = useCallback(async (payment: PaymentItemType, paymentData?: PaymentData) => {
    try {
      await handleMarkAsPaid(payment, paymentData);
      setIsModalOpen(false);
      setSelectedPayment(null);
      setActiveTab('paid');
    } catch (error) {
      console.error('Error confirming payment:', error);
    }
  }, [handleMarkAsPaid]);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedPayment(null);
  }, []);

  const statsCards = useMemo(() => {
    let totalPeriodSubtitle = "Total del Período"; // Default
    if (currentPeriod.displayMode === 'month') {
      if (currentPeriod.isFutureMonth) {
        totalPeriodSubtitle = "Proyección Mensual";
      } else {
        // Ensure displayName is just the month name for this context if it includes year
        // For example, if displayName is "Julio 2024", we might just want "Total de Julio"
        // Or, more simply and consistently, use the full displayName.
        totalPeriodSubtitle = `Total de ${currentPeriod.displayName}`;
      }
    } else { // displayMode === 'range'
      totalPeriodSubtitle = `Total para ${currentPeriod.displayName}`;
    }

    const baseStats = [
      { icon: Clock, title: "Pagos Pendientes", value: payments.pending.length, subtitle: formatCurrency(totals.pending), color: "text-finanz-warning" },
      { icon: AlertTriangle, title: "Pagos Vencidos", value: payments.overdue.length, subtitle: formatCurrency(totals.overdue), color: "text-finanz-danger" },
      { icon: CheckCircle, title: "Pagos Completados", value: payments.paid.length, subtitle: formatCurrency(totals.paidThisMonth), color: "text-finanz-success" },
      {
        icon: DollarSign,
        title: "Total del Período", // Title remains generic
        value: formatCurrency(totals.pending + totals.overdue + totals.paidThisMonth),
        subtitle: totalPeriodSubtitle, // Use the dynamically generated subtitle
        color: "text-finanz-text-primary"
      }
    ];
    // Add projection card only if it's a future month in 'month' view and projections exist
    if (isProjectionDisplayPeriod && hasProjections) {
      baseStats.push({ icon: TrendingUp, title: "Proyecciones del Mes", value: projectedPaymentsForPeriod.length, subtitle: `${projectionAnalysis.confidenceScore}% confianza (${currentPeriod.displayName})`, color: "text-finanz-indigo" });
    }
    return baseStats;
  }, [payments, totals, currentPeriod, isProjectionDisplayPeriod, hasProjections, projectedPaymentsForPeriod, projectionAnalysis]);


  if (isLoading && paymentRecords.length === 0) {
    // Skeleton loader remains the same
    return (
      <div className={`space-y-4 md:space-y-6 animate-pulse ${isMobile ? 'p-3' : 'p-6'}`}>
        <div className="flex items-center justify-between">
          <div>
            <div className={`bg-gray-200 rounded mb-2 ${isMobile ? 'h-6 w-32' : 'h-8 w-64'}`}></div>
            <div className={`bg-gray-200 rounded ${isMobile ? 'h-3 w-24' : 'h-4 w-48'}`}></div>
          </div>
          <div className={`bg-gray-200 rounded ${isMobile ? 'w-6 h-6' : 'w-8 h-8'}`}></div>
        </div>
        <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-4'}`}>
          {[1, 2, 3, 4].map(i => (<div key={i} className={`bg-gray-200 rounded-lg ${isMobile ? 'h-20' : 'h-24'}`}></div>))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">


      <PaymentsHeader
        currentPeriod={currentPeriod}
        periodMetrics={periodMetrics}
        isProjectionPeriod={isProjectionDisplayPeriod} // Use the calculated value
        hasProjections={hasProjections}
        projectionAnalysis={projectionAnalysis}
        projectedPaymentsForPeriod={projectedPaymentsForPeriod}

        selectedDateRange={selectedDateInput} // Pass selectedDateInput from hook
        onDateRangeSelect={setDateRange} // Pass setDateRange from hook

        navigateToPreviousMonth={navigateToPreviousMonth}
        navigateToNextMonth={navigateToNextMonth}
        navigateToCurrentMonth={navigateToCurrentMonth}
        navigateToMonth={navigateToMonth}
        getAvailableMonths={getAvailableMonths}
      />

      <PaymentViewControls
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        onFiltersChange={setFilters} // Corrected: was filters
        totalPayments={allPaymentsList.length}
        filteredCount={filteredPayments.length}
        allPaymentsList={allPaymentsList}
      />

      <PaymentStatsCards stats={statsCards} />

      {viewMode === 'calendar' ? (
        <PaymentCalendarView
          payments={hasActiveFilters ? filteredPayments : allPaymentsList}
          onPaymentSelect={handlePaymentClick}
          selectedDate={selectedCalendarDate}
          onDateSelect={setSelectedCalendarDate}
        />
      ) : viewMode === 'trends' ? (
        <PaymentTrendsAnalysis
          payments={allPaymentsList}
        />
      ) : (
        <PaymentTabsView
          activeTab={activeTab}
          onTabChange={setActiveTab}
          getPaymentsForTab={getPaymentsForTab}
          onPaymentClick={handlePaymentClick}
          onUnmarkAsPaid={handleUnmarkAsPaid}
          currentPeriod={currentPeriod} // Pass currentPeriod for context if needed
          hasActiveFilters={hasActiveFilters}
        />
      )}

      <MarkPaymentAsPaidModal
        payment={selectedPayment}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirmPayment}
      />
    </div>
  );
}
