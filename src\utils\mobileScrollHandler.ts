/**
 * Utilidades para manejar el scroll en dispositivos móviles
 * Soluciona problemas comunes de scroll en el dashboard
 */

export class MobileScrollHandler {
  private static instance: MobileScrollHandler;
  private scrollElements: Map<string, HTMLElement> = new Map();
  private observers: Map<string, IntersectionObserver> = new Map();

  private constructor() {
    this.init();
  }

  public static getInstance(): MobileScrollHandler {
    if (!MobileScrollHandler.instance) {
      MobileScrollHandler.instance = new MobileScrollHandler();
    }
    return MobileScrollHandler.instance;
  }

  private init(): void {
    // Solo ejecutar en dispositivos móviles
    if (window.innerWidth > 767) return;

    // Configurar scroll handlers cuando el DOM esté listo
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupScrollHandlers());
    } else {
      this.setupScrollHandlers();
    }
  }

  private setupScrollHandlers(): void {
    // Buscar y configurar contenedores de scroll
    this.findAndSetupScrollContainers();
    
    // Configurar observers para detectar cambios
    this.setupMutationObserver();
    
    // Configurar listeners para cambios de orientación
    this.setupOrientationHandler();
  }

  private findAndSetupScrollContainers(): void {
    const selectors = [
      '.dashboard-tab-content',
      '.analysis-tab-container',
      '.analysis-content',
      '[role="tabpanel"]'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element, index) => {
        const htmlElement = element as HTMLElement;
        const key = `${selector}-${index}`;
        this.scrollElements.set(key, htmlElement);
        this.setupScrollElement(htmlElement, key);
      });
    });
  }

  private setupScrollElement(element: HTMLElement, key: string): void {
    // Asegurar propiedades de scroll
    element.style.overflowY = 'auto';
    element.style.overflowX = 'hidden';
    element.style.webkitOverflowScrolling = 'touch';
    element.style.scrollBehavior = 'smooth';

    // Agregar listener para detectar scroll
    element.addEventListener('scroll', () => this.handleScroll(element, key), { passive: true });

    // Configurar intersection observer para elementos internos
    this.setupIntersectionObserver(element, key);
  }

  private setupIntersectionObserver(container: HTMLElement, key: string): void {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Elemento visible, asegurar que no tenga restricciones de altura
            const target = entry.target as HTMLElement;
            this.removeHeightRestrictions(target);
          }
        });
      },
      {
        root: container,
        rootMargin: '0px',
        threshold: 0.1
      }
    );

    // Observar todos los elementos hijos
    const children = container.querySelectorAll('.card, .chart-container, [role="tabpanel"]');
    children.forEach(child => observer.observe(child));

    this.observers.set(key, observer);
  }

  private handleScroll(element: HTMLElement, key: string): void {
    const scrollTop = element.scrollTop;
    
    // Agregar clase para indicar scroll activo
    if (scrollTop > 10) {
      element.classList.add('scrolled');
    } else {
      element.classList.remove('scrolled');
    }

    // Verificar si hay elementos que necesitan ajuste
    this.checkAndFixScrollableElements(element);
  }

  private checkAndFixScrollableElements(container: HTMLElement): void {
    // Buscar elementos que puedan estar bloqueando el scroll
    const problematicElements = container.querySelectorAll(
      '.recharts-wrapper, .chart-container, .card, [style*="height: 100vh"], [style*="overflow: hidden"]'
    );

    problematicElements.forEach(element => {
      const htmlElement = element as HTMLElement;
      this.fixElementScrolling(htmlElement);
    });
  }

  private fixElementScrolling(element: HTMLElement): void {
    // Remover restricciones de altura problemáticas
    if (element.style.height === '100vh' || element.style.height === '100%') {
      element.style.height = 'auto';
    }

    // Asegurar overflow visible
    if (element.style.overflow === 'hidden') {
      element.style.overflow = 'visible';
    }

    // Casos específicos para gráficos
    if (element.classList.contains('recharts-wrapper')) {
      element.style.overflow = 'visible';
      element.style.height = 'auto';
    }
  }

  private removeHeightRestrictions(element: HTMLElement): void {
    // Lista de propiedades problemáticas
    const problematicStyles = [
      'height: 100vh',
      'height: 100%',
      'max-height: 100vh',
      'overflow: hidden',
      'position: fixed'
    ];

    problematicStyles.forEach(style => {
      const [property, value] = style.split(': ');
      if (element.style.getPropertyValue(property) === value) {
        if (property === 'height' || property === 'max-height') {
          element.style.setProperty(property, 'auto', 'important');
        } else if (property === 'overflow') {
          element.style.setProperty(property, 'visible', 'important');
        }
      }
    });
  }

  private setupMutationObserver(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          // Nuevos elementos agregados, verificar si necesitan configuración
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as HTMLElement;
              this.checkNewElement(element);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  private checkNewElement(element: HTMLElement): void {
    // Verificar si el nuevo elemento es un contenedor de scroll
    const scrollSelectors = ['.dashboard-tab-content', '.analysis-content', '[role="tabpanel"]'];
    
    if (scrollSelectors.some(selector => element.matches(selector))) {
      const key = `dynamic-${Date.now()}`;
      this.scrollElements.set(key, element);
      this.setupScrollElement(element, key);
    }

    // Verificar elementos hijos
    const scrollChildren = element.querySelectorAll(scrollSelectors.join(', '));
    scrollChildren.forEach((child, index) => {
      const htmlChild = child as HTMLElement;
      const key = `dynamic-child-${Date.now()}-${index}`;
      this.scrollElements.set(key, htmlChild);
      this.setupScrollElement(htmlChild, key);
    });
  }

  private setupOrientationHandler(): void {
    window.addEventListener('orientationchange', () => {
      // Esperar a que la orientación se complete
      setTimeout(() => {
        this.recalculateScrollContainers();
      }, 100);
    });

    window.addEventListener('resize', () => {
      // Solo en móviles
      if (window.innerWidth <= 767) {
        this.recalculateScrollContainers();
      }
    });
  }

  private recalculateScrollContainers(): void {
    this.scrollElements.forEach((element, key) => {
      if (element && element.isConnected) {
        // Forzar recálculo de dimensiones
        element.style.height = 'auto';
        
        // Verificar y corregir scroll
        setTimeout(() => {
          this.checkAndFixScrollableElements(element);
        }, 50);
      }
    });
  }

  // Método público para forzar corrección de scroll
  public forceScrollFix(): void {
    this.findAndSetupScrollContainers();
    this.recalculateScrollContainers();
  }

  // Método público para limpiar observers
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.scrollElements.clear();
  }

  // Método público para verificar si el scroll está funcionando
  public isScrollWorking(containerId?: string): boolean {
    const containers = containerId 
      ? [document.getElementById(containerId)].filter(Boolean)
      : Array.from(this.scrollElements.values());

    return containers.some(container => {
      if (!container) return false;
      return container.scrollHeight > container.clientHeight;
    });
  }
}

// Hook para usar en componentes React
export const useMobileScrollFix = () => {
  const scrollHandler = MobileScrollHandler.getInstance();

  const forceScrollFix = () => scrollHandler.forceScrollFix();
  const isScrollWorking = (containerId?: string) => scrollHandler.isScrollWorking(containerId);

  return {
    forceScrollFix,
    isScrollWorking
  };
};

// Inicializar automáticamente en dispositivos móviles
if (typeof window !== 'undefined' && window.innerWidth <= 767) {
  MobileScrollHandler.getInstance();
}
