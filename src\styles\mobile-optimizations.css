/* Optimizaciones móviles completas y avanzadas con enfoque Mobile-First */

/* Transiciones de página optimizadas */
.page-enter {
  opacity: 0;
  transform: translateX(20px);
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 200ms ease-out, transform 200ms ease-out;
}

.page-exit {
  opacity: 1;
  transform: translateX(0);
}

.page-exit-active {
  opacity: 0;
  transform: translateX(-20px);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Transiciones desktop más suaves */
@media (min-width: 768px) {
  .page-enter {
    opacity: 0;
    transform: translateY(10px);
  }
  
  .page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 300ms ease-out, transform 300ms ease-out;
  }
  
  .page-exit {
    opacity: 1;
    transform: translateY(0);
  }
  
  .page-exit-active {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 300ms ease-in, transform 300ms ease-in;
  }
}

/* Variables CSS para móvil mejoradas */
:root {
  --mobile-touch-target: 44px;
  --mobile-spacing-xs: 0.25rem;
  --mobile-spacing-sm: 0.5rem;
  --mobile-spacing-md: 0.75rem;
  --mobile-spacing-lg: 1rem;
  --mobile-spacing-xl: 1.25rem;
  
  /* Tipografía responsiva con rem */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  
  --mobile-border-radius: 0.375rem;
  --mobile-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

/* ==============================================
   ESTILOS BASE MOBILE-FIRST 
   ============================================== */

/* Tipografía base mobile-first */
html {
  font-size: 16px; /* Base para rem */
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-size: var(--font-size-base);
  line-height: 1.5;
}

/* Encabezados mobile-first */
h1 {
  font-size: var(--font-size-2xl);
  line-height: 1.2;
}

h2 {
  font-size: var(--font-size-xl);
  line-height: 1.3;
}

h3 {
  font-size: var(--font-size-lg);
  line-height: 1.4;
}

h4 {
  font-size: var(--font-size-base);
  line-height: 1.4;
}

h5 {
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

h6 {
  font-size: var(--font-size-xs);
  line-height: 1.5;
}

/* ==============================================
   GRILLAS Y LAYOUTS MOBILE-FIRST
   ============================================== */

/* Grillas base - 1 columna en móvil */
.responsive-grid,
.expense-grid,
.income-grid,
.debt-grid,
.card-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--mobile-spacing-md);
}

/* Contenedores responsivos */
.responsive-container {
  width: 100%;
  padding: var(--mobile-spacing-sm);
}

/* ==============================================
   FORMULARIOS MOBILE-FIRST
   ============================================== */

/* Todos los formularios 100% ancho en móvil */
form,
.form-container {
  width: 100%;
}

input,
textarea,
select,
button,
.input,
.textarea,
.select,
.button {
  width: 100%;
  font-size: 16px; /* Evita zoom en iOS */
  padding: var(--mobile-spacing-md);
  border-radius: var(--mobile-border-radius);
}

/* Grupos de formulario */
.form-group,
.input-group {
  margin-bottom: var(--mobile-spacing-lg);
}

/* Labels de formulario */
label,
.label {
  display: block;
  margin-bottom: var(--mobile-spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* ==============================================
   NAVEGACIÓN MOBILE-FIRST
   ============================================== */

/* Sidebar oculto por defecto en móvil (manejado por SidebarProvider) */
@media (max-width: 767px) {
  /* El menú hamburguesa ya está implementado con SidebarTrigger */
  .sidebar-mobile-overlay {
    position: fixed;
    inset: 0;
    z-index: 40;
    background: rgba(0, 0, 0, 0.5);
  }
}

/* ==============================================
   IMÁGENES Y GRÁFICOS RESPONSIVOS
   ============================================== */

img,
picture,
video,
canvas,
svg,
.chart-container,
.image-container {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Contenedores de gráficos */
.chart-wrapper,
.graph-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* ==============================================
   MEDIA QUERIES PARA TABLET (768px - 1023px)
   ============================================== */

@media (min-width: 768px) {
  /* Tipografía tablet */
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
  h4 { font-size: var(--font-size-lg); }
  
  /* Grillas tablet - 2 columnas */
  .responsive-grid,
  .expense-grid,
  .income-grid,
  .debt-grid,
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--mobile-spacing-lg);
  }
  
  /* Contenedores tablet */
  .responsive-container {
    padding: var(--mobile-spacing-md);
  }
  
  /* Formularios pueden ser más estrechos en tablet */
  .form-narrow {
    max-width: 600px;
    margin: 0 auto;
  }
}

/* ==============================================
   MEDIA QUERIES PARA ESCRITORIO (1024px+)
   ============================================== */

@media (min-width: 1024px) {
  /* Tipografía escritorio */
  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }
  h4 { font-size: var(--font-size-xl); }
  
  /* Grillas escritorio - 3+ columnas según contexto */
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--mobile-spacing-xl);
  }
  
  .expense-grid,
  .income-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .debt-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .card-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Contenedores escritorio */
  .responsive-container {
    padding: var(--mobile-spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
  }
  
  /* Formularios en escritorio */
  input,
  textarea,
  select,
  button {
    width: auto; /* No necesariamente 100% en escritorio */
  }
  
  .form-full-width input,
  .form-full-width textarea,
  .form-full-width select,
  .form-full-width button {
    width: 100%;
  }
}

/* ==============================================
   OPTIMIZACIONES ADICIONALES MÓVILES
   ============================================== */

@media (max-width: 767px) {
  /* ... existing mobile optimizations ... */
  
  /* Cards más compactas */
  .card,
  [class*="card"] {
    margin-bottom: var(--mobile-spacing-md);
    border-radius: var(--mobile-border-radius);
    box-shadow: var(--mobile-shadow);
  }
  
  /* Padding reducido en móvil */
  .card-header,
  .card-content,
  .card-footer {
    padding: var(--mobile-spacing-md);
  }
  
  /* Botones full width en móvil */
  .btn,
  .button,
  button[type="submit"],
  button[type="button"] {
    width: 100%;
    justify-content: center;
    min-height: var(--mobile-touch-target);
  }

  /* Grupos de botones apilados en móvil */
  .button-group,
  .btn-group {
    display: flex;
    flex-direction: column;
    gap: var(--mobile-spacing-sm);
  }

  /* Optimizaciones específicas para botones de análisis */
  .analysis-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mobile-spacing-xs);
    width: 100%;
  }

  .analysis-buttons button {
    font-size: var(--font-size-xs);
    padding: var(--mobile-spacing-xs) var(--mobile-spacing-sm);
    min-height: 32px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Tabs responsivos mejorados */
  .dashboard-tabs {
    background-color: var(--background);
    border-radius: var(--mobile-border-radius);
    padding: var(--mobile-spacing-xs);
  }

  .dashboard-tab-trigger {
    min-height: var(--mobile-touch-target);
    font-size: var(--font-size-xs);
    font-weight: 600;
    border-radius: calc(var(--mobile-border-radius) - 2px);
    transition: all 0.2s ease;
  }

  .dashboard-tab-trigger:active {
    transform: scale(0.95);
  }

  .dashboard-tab-trigger[data-state="active"] {
    background-color: white;
    box-shadow: var(--mobile-shadow);
  }

  /* Dark mode support */
  .dark .dashboard-tabs {
    background-color: rgb(31 41 55);
  }

  .dark .dashboard-tab-trigger[data-state="active"] {
    background-color: rgb(55 65 81);
    color: white;
  }

  .dark .analysis-buttons button {
    border-color: rgb(75 85 99);
    background-color: rgb(31 41 55);
    color: rgb(229 231 235);
  }

  .dark .analysis-buttons button:hover {
    background-color: rgb(55 65 81);
  }

  .dark .analysis-buttons button[data-state="active"] {
    background-color: rgb(59 130 246);
    color: white;
    border-color: rgb(59 130 246);
  }
  
  /* Tablas responsivas */
  table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Modales full screen en móvil */
  .modal,
  .dialog,
  [role="dialog"] {
    position: fixed;
    inset: 0;
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    margin: 0;
    border-radius: 0;
  }
}

/* ==============================================
   UTILIDADES RESPONSIVAS
   ============================================== */

/* Clases de visibilidad mejoradas */
.mobile-only { display: block; }
.tablet-only { display: none; }
.desktop-only { display: none; }

@media (min-width: 768px) {
  .mobile-only { display: none; }
  .tablet-only { display: block; }
  .desktop-only { display: none; }
}

@media (min-width: 1024px) {
  .mobile-only { display: none; }
  .tablet-only { display: none; }
  .desktop-only { display: block; }
}

/* ==============================================
   OPTIMIZACIONES DE RENDIMIENTO
   ============================================== */

/* Reducir animaciones en móvil */
@media (max-width: 767px) {
  *, *::before, *::after {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
  }
}

/* ... existing code continues ... */

/* Optimizaciones para dispositivos móviles */
@media (max-width: 767px) {
  /* Scroll suave y sin bounce */
  * {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  body {
    overscroll-behavior: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }

  /* Touch targets mínimos mejorados */
  button, 
  [role="button"],
  input[type="button"],
  input[type="submit"],
  .touch-target,
  .touch-button {
    min-height: var(--mobile-touch-target);
    min-width: var(--mobile-touch-target);
    touch-action: manipulation;
  }

  /* Efectos táctiles mejorados */
  .touch-feedback {
    transition: all 0.15s ease;
    user-select: none;
    -webkit-user-select: none;
  }

  .touch-feedback:active {
    transform: scale(0.95);
    opacity: 0.8;
  }

  .touch-feedback:focus {
    outline: 2px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
  }

  /* Navegación móvil mejorada */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 0.5rem;
    padding-bottom: env(safe-area-inset-bottom);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Modal full-screen en móvil mejorado */
  .mobile-modal {
    position: fixed;
    inset: 0;
    z-index: 50;
    background: white;
    overflow-y: auto;
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Optimización de scroll mejorada */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    scroll-behavior: smooth;
  }

  .mobile-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Headers móviles optimizados */
  .mobile-header {
    position: sticky;
    top: 0;
    z-index: 40;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding: var(--mobile-spacing-sm) var(--mobile-spacing-md);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Listas móviles optimizadas */
  .mobile-list-item {
    padding: var(--mobile-spacing-md);
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: var(--mobile-spacing-sm);
  }

  .mobile-list-item:last-child {
    border-bottom: none;
  }

  /* Espaciado móvil expandido */
  .mobile-p-1 { padding: var(--mobile-spacing-xs); }
  .mobile-p-2 { padding: var(--mobile-spacing-sm); }
  .mobile-p-3 { padding: var(--mobile-spacing-md); }
  .mobile-p-4 { padding: var(--mobile-spacing-lg); }
  .mobile-p-5 { padding: var(--mobile-spacing-xl); }

  .mobile-px-1 { padding-left: var(--mobile-spacing-xs); padding-right: var(--mobile-spacing-xs); }
  .mobile-px-2 { padding-left: var(--mobile-spacing-sm); padding-right: var(--mobile-spacing-sm); }
  .mobile-px-3 { padding-left: var(--mobile-spacing-md); padding-right: var(--mobile-spacing-md); }

  .mobile-py-1 { padding-top: var(--mobile-spacing-xs); padding-bottom: var(--mobile-spacing-xs); }
  .mobile-py-2 { padding-top: var(--mobile-spacing-sm); padding-bottom: var(--mobile-spacing-sm); }
  .mobile-py-3 { padding-top: var(--mobile-spacing-md); padding-bottom: var(--mobile-spacing-md); }

  .mobile-m-1 { margin: var(--mobile-spacing-xs); }
  .mobile-m-2 { margin: var(--mobile-spacing-sm); }
  .mobile-m-3 { margin: var(--mobile-spacing-md); }
  .mobile-m-4 { margin: var(--mobile-spacing-lg); }

  .mobile-mb-1 { margin-bottom: var(--mobile-spacing-xs); }
  .mobile-mb-2 { margin-bottom: var(--mobile-spacing-sm); }
  .mobile-mb-3 { margin-bottom: var(--mobile-spacing-md); }
  .mobile-mb-4 { margin-bottom: var(--mobile-spacing-lg); }

  /* Estados interactivos mejorados */
  .mobile-hover:active {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark .mobile-hover:active {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .mobile-focus:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  .mobile-disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

/* Safe area support mejorado */
@supports (padding: max(0px)) {
  .safe-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .safe-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  .safe-area-inset {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Animaciones reducidas para motion reducido */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .mobile-scroll {
    scroll-behavior: auto;
  }
}

/* Dark mode móvil mejorado */
@media (max-width: 767px) {
  .dark {
    --mobile-bg: theme(colors.gray.900);
    --mobile-surface: theme(colors.gray.800);
    --mobile-border: theme(colors.gray.700);
    --mobile-text: theme(colors.gray.100);
    --mobile-text-muted: theme(colors.gray.400);
  }

  .dark .mobile-nav {
    background: var(--mobile-surface);
    border-top-color: var(--mobile-border);
  }

  .dark .mobile-modal {
    background: var(--mobile-bg);
  }

  .dark .mobile-header {
    background: var(--mobile-surface);
    border-bottom-color: var(--mobile-border);
  }
}

/* Performance optimizations mejoradas */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-scroll {
  will-change: scroll-position;
}

.will-change-transform {
  will-change: transform;
}

/* Landscape optimizations mejoradas */
@media (max-width: 767px) and (orientation: landscape) {
  .mobile-landscape-compact {
    padding: 0.25rem;
  }
  
  .mobile-landscape-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }

  .mobile-landscape-header {
    height: 48px;
    padding: 0.25rem 0.5rem;
  }

  .mobile-landscape-content {
    height: calc(100vh - 48px);
    overflow-y: auto;
  }
}

/* Utilidades de contenedor móvil */
@media (max-width: 767px) {
  .mobile-container {
    padding-left: var(--mobile-spacing-md);
    padding-right: var(--mobile-spacing-md);
  }

  .mobile-container-sm {
    padding-left: var(--mobile-spacing-sm);
    padding-right: var(--mobile-spacing-sm);
  }

  .mobile-container-lg {
    padding-left: var(--mobile-spacing-lg);
    padding-right: var(--mobile-spacing-lg);
  }
}

/* Extra small screen tweaks */
@media (max-width: 640px) {
  html {
    font-size: 13px;
  }

  :root {
    --mobile-spacing-sm: 0.4rem;
    --mobile-spacing-md: 0.6rem;
    --mobile-spacing-lg: 0.8rem;
  }

  .mobile-container {
    padding-left: var(--mobile-spacing-sm);
    padding-right: var(--mobile-spacing-sm);
  }
}
